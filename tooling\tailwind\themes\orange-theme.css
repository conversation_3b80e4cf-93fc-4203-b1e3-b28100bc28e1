html {
	scroll-behavior: smooth;
}

/* ------------------------------ Base Layer ------------------------------ */
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
	}

	button:not(:disabled),
	[role="button"]:not(:disabled),
	[role="menuitem"]:not(:disabled) {
		cursor: pointer;
	}
}

/* ------------------------------ Root Variables ------------------------------ */
:root {
	/* Colors */
	--background: oklch(1 0 0);
	--foreground: oklch(0.1884 0.0128 248.5103);
	--card: oklch(0.9784 0.0011 197.1387);
	--card-foreground: oklch(0.1884 0.0128 248.5103);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.1884 0.0128 248.5103);
	--primary: oklch(0.7576 0.159 55.9344);
	--primary-foreground: oklch(1 0 0);
	--secondary: oklch(0.1884 0.0128 248.5103);
	--secondary-foreground: oklch(1 0 0);
	--muted: oklch(0.9222 0.0013 286.3737);
	--muted-foreground: oklch(0.1884 0.0128 248.5103);
	--accent: oklch(0.9392 0.0166 250.8453);
	--accent-foreground: oklch(0.7576 0.159 55.9344);
	--destructive: oklch(0.6188 0.2376 25.7658);
	--destructive-foreground: oklch(1 0 0);
	--border: oklch(0.9317 0.0118 231.6594);
	--input: oklch(0.9809 0.0025 228.7836);
	--ring: oklch(0.7576 0.159 55.9344);
	--warning: hsl(38 92% 50%);
	--warning-foreground: hsl(48 96% 89%);

	/* Fonts */
	--font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
		"Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
		"Noto Color Emoji";
	--font-serif: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);

	/* Radius */
	--radius: 0.75rem;
	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	/* Shadows */
	--shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
	--shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
	--shadow-sm: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
		0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
	--shadow: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
		0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
	--shadow-md: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
		0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0);
	--shadow-lg: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
		0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0);
	--shadow-xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
		0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0);
	--shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);

	/* Accordion Animations */
	--animation-accordion-down: accordion-down 0.2s ease-out;
	--animation-accordion-up: accordion-up 0.2s ease-out;

	/* Fumadocs */
	--fd-banner-height: 4.5rem;
}

/* ------------------------------ Dark Mode ------------------------------ */
.dark {
	--background: oklch(0.1469 0.0041 49.2499);
	--foreground: oklch(0.9699 0.0013 106.4238);
	--card: oklch(0.2161 0.0061 56.0434);
	--card-foreground: oklch(0.9699 0.0013 106.4238);
	--popover: oklch(0.2161 0.0061 56.0434);
	--popover-foreground: oklch(0.9699 0.0013 106.4238);
	--primary: oklch(0.7576 0.159 55.9344);
	--primary-foreground: oklch(0.2095 0.0163 71.0606);
	--secondary: oklch(0.2686 0 0);
	--secondary-foreground: oklch(0.9699 0.0013 106.4238);
	--muted: oklch(0.2685 0.0063 34.2976);
	--muted-foreground: oklch(0.7161 0.0091 56.259);
	--accent: oklch(0.2686 0 0);
	--accent-foreground: oklch(0.9699 0.0013 106.4238);
	--destructive: oklch(0.4437 0.1613 26.8994);
	--destructive-foreground: oklch(0.9356 0.0309 17.7172);
	--border: oklch(0.2685 0.0063 34.2976);
	--input: oklch(0.2685 0.0063 34.2976);
	--ring: oklch(0.7576 0.159 55.9344);
	--warning: hsl(48 96% 89%);
	--warning-foreground: hsl(38 92% 50%);
}

/* ------------------------------ Tailwind Theme Mappings ------------------------------ */
@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-warning: var(--warning);
	--color-warning-foreground: var(--warning-foreground);

	--font-sans: var(--font-sans);
	--font-mono: var(--font-mono);
	--font-serif: var(--font-serif);

	--radius-sm: var(--radius-sm);
	--radius-md: var(--radius-md);
	--radius-lg: var(--radius-lg);

	--shadow-2xs: var(--shadow-2xs);
	--shadow-xs: var(--shadow-xs);
	--shadow-sm: var(--shadow-sm);
	--shadow: var(--shadow);
	--shadow-md: var(--shadow-md);
	--shadow-lg: var(--shadow-lg);
	--shadow-xl: var(--shadow-xl);
	--shadow-2xl: var(--shadow-2xl);
}

/* ------------------------------ Shadows ------------------------------ */
.shadow-light {
	box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.085);
}

.shadow-dark {
	box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.141);
}

/* ------------------------------ Accordion Animations ------------------------------ */
@keyframes accordion-down {
	from {
		height: 0;
	}
	to {
		height: var(--radix-accordion-content-height);
	}
}

@keyframes accordion-up {
	from {
		height: var(--radix-accordion-content-height);
	}
	to {
		height: 0;
	}
}

/* ------------------------------ Marquee Utilities ------------------------------ */
@keyframes marquee {
	from {
		transform: translateX(0%);
	}
	to {
		transform: translateX(-100%);
	}
}

@keyframes marquee-vertical {
	from {
		transform: translateY(0%);
	}
	to {
		transform: translateY(-100%);
	}
}

@layer utilities {
	.animate-marquee {
		animation: marquee var(--duration) linear infinite;
	}
	.animate-marquee-vertical {
		animation: marquee-vertical var(--duration) linear infinite;
	}
}

/* ------------------------------ Selection ------------------------------ */
::selection {
	background: var(--primary);
	color: black;
	-webkit-text-fill-color: black;
}
::-moz-selection {
	background: var(--primary);
	color: black;
	-webkit-text-fill-color: black;
}
