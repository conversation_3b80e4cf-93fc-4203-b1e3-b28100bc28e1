import type { RouterClient } from "@orpc/server";
import { protectedProcedure, publicProcedure } from "../../lib/orpc";
import { orpcTest } from "./test";

export const orpcRouter = {
	test: orpcTest,
	// users: usersRouter,
	// chats: chatsRouter,
	// Add more routers here
	healthCheck: publicProcedure.handler(() => {
		return "OK";
	}),
	privateData: protectedProcedure.handler(({ context }) => {
		return {
			message: "This is private",
			user: context.session?.user,
		};
	}),
};
export type orpcRouter = typeof orpcRouter;
export type ORPCRouterClient = RouterClient<typeof orpcRouter>;
