{"dependencies": {"@orpc/client": "^1.9.3", "@orpc/openapi": "^1.9.3", "@orpc/server": "^1.9.3", "@orpc/zod": "^1.9.3", "@repo/ai": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@scalar/hono-api-reference": "^0.9.3", "@sindresorhus/slugify": "^2.2.1", "hono": "^4.7.11", "hono-openapi": "^0.4.8", "nanoid": "^5.1.5", "openai": "^5.1.1", "openapi-merge": "^1.3.3", "use-intl": "^4.1.0", "zod": "^3.25.55"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/react": "19.1.6", "encoding": "^0.1.13", "prisma": "^6.9.0", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@repo/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}