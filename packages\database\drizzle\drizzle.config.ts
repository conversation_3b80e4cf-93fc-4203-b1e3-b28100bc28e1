import { defineConfig } from "drizzle-kit";

let dbEngine = "postgres";
if (process.env.DATABASE_URL?.startsWith("mysql")) {
	dbEngine = "mysql";
} else if (process.env.DATABASE_URL?.startsWith("sqlite")) {
	dbEngine = "sqlite";
}
export default defineConfig({
	dialect: dbEngine === "mysql" ? "mysql" : dbEngine === "sqlite" ? "sqlite" : "postgresql",
	schema: `./drizzle/schema/${dbEngine}.ts`,
	out: `./drizzle/migrations/${dbEngine}`,
	dbCredentials: {
		url: process.env.DATABASE_URL as string,
	},
});
