import { z } from "zod";
import { protectedProcedure, publicProcedure } from "../../lib/orpc";

export const orpcTest = {
	hello: publicProcedure
		.input(z.object({ name: z.string().optional() }).optional())
		.output(z.object({ message: z.string() }))
		.handler(async ({ ...args }) => {
			console.log("Input received in hello:", args);
			return {
				message: `oRPC says hello ${args.input?.name ?? "world"}`,
			};
		}),
	private: protectedProcedure
		.input(z.object({ name: z.string().optional() }).optional())
		.output(z.object({ message: z.string() }))
		.handler(async ({ input }) => {
			return {
				message: `oRPC checked auth and says hello ${input?.name ?? "world"}`,
			};
		}),
};
